'use client';

import { useEffect, useState, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { ShoppingCart, X, Minus, Plus, Trash as TrashIcon } from 'lucide-react';
import { useCartStore } from '@/store/useCartStore';
import { useCurrency } from '@/contexts/CurrencyContext';

// Define Product type based on our schema
type Product = {
  _id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  imageUrl?: string;
  videoUrl?: string;
  createdAt: string;
}

// Define CartItem type
type CartItem = {
  product: Product;
  quantity: number;
}

// Animation keyframes
const animationStyles = `
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
    }
    to {
      transform: translateX(0);
    }
  }
  
  @keyframes slideOutRight {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(100%);
    }
  }
  
  .animate-slide-in-right {
    animation: slideInRight 0.3s ease-out forwards;
  }
  
  .animate-slide-out-right {
    animation: slideOutRight 0.3s ease-in forwards;
  }
`;

export default function CartDrawer() {
  const {
    items,
    isOpen,
    closeCart,
    updateQuantity,
    removeItem,
    getTotal,
    syncWithDatabase,
    clearCart
  } = useCartStore();

  const { convertPrice, formatPrice } = useCurrency();
  const router = useRouter();

  // Helper function to get the correct image URL
  const getProductImageUrl = (imageUrl: string): string => {
    if (!imageUrl) return '/images/sphene.jpg';

    // If it's already a full URL, return as is
    if (imageUrl.startsWith('http') || imageUrl.startsWith('/')) {
      return imageUrl;
    }

    // If it's just a filename, construct CloudFront URL
    if (process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN) {
      return `${process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN}/${imageUrl}`;
    }

    // Fallback
    return '/images/sphene.jpg';
  };
  
  // Check if component is mounted (for hydration safety)
  const [mounted, setMounted] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const cartDrawerRef = useRef<HTMLDivElement>(null);
  
  // Handle drawer closing with animation
  const handleCloseCart = () => {
    setIsClosing(true);
    // Wait for animation to complete before actually closing
    setTimeout(() => {
      closeCart();
      setIsClosing(false);
    }, 300); // Match animation duration (300ms)
  };
  
  // Handle checkout button click with proper animation sequence
  const handleCheckoutClick = () => {
    // Start the cart closing animation first
    setIsClosing(true);
    
    // Wait for the animation to complete, then navigate to checkout
    setTimeout(() => {
      closeCart();
      setIsClosing(false);
      // Navigate to checkout page after animation completes
      router.push('/checkout');
    }, 300); // Match animation duration (300ms)
  };
  
  // Sync cart when opened
  useEffect(() => {
    if (mounted && isOpen && items.length > 0) {
      console.log('[CartDrawer] Cart opened, syncing with database');
      syncWithDatabase();
    }
  }, [isOpen, mounted, items.length, syncWithDatabase]);
  
  // Force sync cart after mounting and whenever items change
  useEffect(() => {
    if (mounted && items.length > 0) {
      console.log('[CartDrawer] Items updated, syncing with database');
      syncWithDatabase();
    }
  }, [items, mounted, syncWithDatabase]);
  
  useEffect(() => {
    setMounted(true);
    
    // Force first sync after mount
    setTimeout(() => {
      const store = useCartStore.getState();
      if (store.items.length > 0) {
        console.log('[CartDrawer] Initial sync after mounting');
        store.syncWithDatabase();
      }
    }, 1000);
    
    // Track inactivity time for cart abandonment
    const trackInactivity = () => {
      const store = useCartStore.getState();
      
      // Only track if cart has items
      if (store.items.length > 0) {
        // Sync with database before user leaves
        console.log('[CartDrawer] Activity tracking - syncing cart');
        store.syncWithDatabase();
      }
    };
    
    // Add event listeners for page unload/visibility change
    window.addEventListener('beforeunload', trackInactivity);
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        trackInactivity();
      }
    });
    
    // Add inactivity timer (5 minutes)
    let inactivityTimer: NodeJS.Timeout;
    
    const resetInactivityTimer = () => {
      clearTimeout(inactivityTimer);
      inactivityTimer = setTimeout(() => {
        trackInactivity();
      }, 5 * 60 * 1000); // 5 minutes
    };
    
    // User actions that reset the timer
    window.addEventListener('mousemove', resetInactivityTimer);
    window.addEventListener('keypress', resetInactivityTimer);
    window.addEventListener('scroll', resetInactivityTimer);
    window.addEventListener('click', resetInactivityTimer);
    
    // Start the timer
    resetInactivityTimer();
    
    return () => {
      // Clean up event listeners
      window.removeEventListener('beforeunload', trackInactivity);
      document.removeEventListener('visibilitychange', trackInactivity);
      window.removeEventListener('mousemove', resetInactivityTimer);
      window.removeEventListener('keypress', resetInactivityTimer);
      window.removeEventListener('scroll', resetInactivityTimer);
      window.removeEventListener('click', resetInactivityTimer);
      clearTimeout(inactivityTimer);
      setMounted(false);
    };
  }, []);
  
  // Return null during server rendering to avoid hydration mismatch
  if (!mounted) {
    return null;
  }
  
  // Update total and item prices to reflect selected currency
  const displayTotal = formatPrice(getTotal());
  
  return (
    <>
      {/* Insert animation styles */}
      <style>{animationStyles}</style>
      
      {/* Cart Drawer */}
      {(isOpen || isClosing) && (
        <div className="fixed inset-0 z-50 overflow-hidden">
          {/* Backdrop */}
          <div 
            className="absolute inset-0 bg-black/30" 
            onClick={handleCloseCart}
          />
          
          {/* Drawer */}
          <div 
            ref={cartDrawerRef}
            className={`absolute top-0 right-0 w-full max-w-md h-full bg-[#f8f8f8] shadow-lg transform ${
              isClosing ? 'animate-slide-out-right' : 'animate-slide-in-right'
            }`}
          >
            <div className="flex flex-col h-full">
              {/* Cart Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-300">
                <h2 className="text-xl font-semibold">Your Cart</h2>
                <button 
                  onClick={handleCloseCart}
                  className="p-1 rounded-full hover:bg-gray-100"
                  aria-label="Close cart"
                >
                  <X size={24} />
                </button>
              </div>
              
              {/* Cart Items */}
              <div className="flex-grow overflow-y-auto p-4">
                {items.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <ShoppingCart size={48} className="mb-2 opacity-30" />
                    <p>Your cart is empty</p>
                  </div>
                ) : (
                  <ul className="space-y-4">
                    {items.map((item) => (
                      <li key={item.product._id} className="flex items-center gap-4 py-4 border-b">
                        {/* Product Image */}
                        <div className="relative h-16 w-16 rounded overflow-hidden flex-shrink-0 bg-gray-100">
                          {item.product.imageUrl ? (
                            <Image
                              src={getProductImageUrl(item.product.imageUrl)}
                              alt={item.product.name}
                              fill
                              sizes="64px"
                              className="object-cover"
                              onError={(e) => {
                                // Fallback to default image if image fails to load
                                const target = e.target as HTMLImageElement;
                                target.src = '/images/sphene.jpg';
                              }}
                            />
                          ) : (
                            // Fallback image when no imageUrl is available
                            <Image
                              src="/images/sphene.jpg"
                              alt={item.product.name}
                              fill
                              sizes="64px"
                              className="object-cover opacity-60"
                            />
                          )}
                        </div>
                        
                        {/* Product Details */}
                        <div className="flex-grow">
                          <h3 className="font-medium">{item.product.name}</h3>
                          <p className="text-blue-600">{formatPrice(item.product.price)}</p>
                        </div>
                        
                        {/* Quantity Control */}
                        <div className="flex items-center gap-2">
                          <button 
                            onClick={() => updateQuantity(item.product._id, item.quantity - 1)}
                            className="p-1 rounded-full hover:bg-gray-100"
                            aria-label="Decrease quantity"
                          >
                            <Minus size={16} />
                          </button>
                          
                          <span className="w-8 text-center">{item.quantity}</span>
                          
                          <button 
                            onClick={() => updateQuantity(item.product._id, item.quantity + 1)}
                            className="p-1 rounded-full hover:bg-gray-100"
                            aria-label="Increase quantity"
                          >
                            <Plus size={16} />
                          </button>
                          
                          <button 
                            onClick={() => removeItem(item.product._id)}
                            className="ml-2 p-1 rounded-full hover:bg-red-100 text-red-500"
                            aria-label="Remove item"
                          >
                            <TrashIcon size={16} />
                          </button>
                        </div>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
              
              {/* Cart Footer */}
              {items.length > 0 && (
                <div className="p-4 border-t border-gray-300 mt-auto">
                  <div className="flex justify-between mb-4">
                    <span className="font-semibold text-gray-800">Total:</span>
                    <span className="font-semibold text-gray-800">
                      {displayTotal}
                    </span>
                  </div>
                  
                  <button
                    onClick={handleCheckoutClick}
                    className="w-full block py-2 px-4 bg-gradient-to-tl from-[#51575F] to-[#1F2937] text-white rounded text-center font-medium hover:from-[#6B7280] hover:to-[#374151] active:from-[#4B5563] active:to-[#111827] transition-all duration-200 shadow-md hover:shadow-lg active:shadow-sm active:scale-95 hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-[#a3003f]/50 mb-2"
                  >
                    Checkout
                  </button>
                  
                  <button
                    onClick={() => {
                      clearCart();
                      handleCloseCart();
                    }}
                    className="w-full block py-2 px-4 bg-gray-200 text-gray-700 rounded text-center font-medium hover:bg-gray-300 transition-colors"
                  >
                    Clear Cart and Shop Again
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
} 